apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: b2b-event-processor-service
  namespace: argocd
spec:
  project: b2b-pre-production-apps
  source:
    repoURL: ****************************:D9/telematics-applications/b2b-platform/b2b-event-processor-service.git
    targetRevision: dev
    path: k8s/b2b-pre-production
  destination:
    server: https://kubernetes.default.svc
    namespace: telematics
  syncPolicy:
    automated:
      prune: true
    syncOptions:
    - CreateNamespace=true
    - Validate=false
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
