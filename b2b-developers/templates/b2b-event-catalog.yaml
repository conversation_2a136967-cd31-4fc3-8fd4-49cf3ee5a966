apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: b2b-event-catalog
  namespace: argocd
spec:
  project: b2b-developers-apps
  source:
    repoURL: ****************************:D9/architecture/vcdp-event-catalog.git
    targetRevision: main
    path: k8s/b2b-developers
  destination:
    server: https://kubernetes.default.svc
    namespace: telematics
  syncPolicy:
    automated:
      prune: true
    syncOptions:
    - CreateNamespace=true
    - Validate=false
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
