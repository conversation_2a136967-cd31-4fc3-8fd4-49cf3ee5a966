apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: datadog-synthetic-private-location
  namespace: argocd
spec:
  project: cluster-addons
  destination:
    server: https://kubernetes.default.svc
    namespace: datadog
  source:
    repoURL: ****************************:D9/pre-sre/aws/datado-private-location-helm-chart.git
    targetRevision: main
    path: .
    helm:
      releaseName: datadog-synthetic-private-location
      values: |
        global:
          region: "eu-west-2"

        secretsManager:
          enabled: true
          secretProviderClass:
            name: "datadog-private-location-secret"
            secretName: "arn:aws:secretsmanager:eu-west-2:************:secret:secret/datadog/private-location-worker/config-Vc5qFt"
            secretVersionLabel: "AWSCURRENT"

          targetSecret:
            name: "datadog-private-location-config"

        datadog:
          enabled: true

        synthetics-private-location:
          serviceAccount: 
            create: true
            name: "datadog-private-location-sa"
            annotations:
              eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/datadog-private-location-role"
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 200m
              memory: 256Mi
          extraVolumes:
          - name: worker-config
            csi:
              driver:  secrets-store.csi.k8s.io
              readOnly: true
              volumeAttributes:
                secretProviderClass: "datadog-private-location-secret"
          extraVolumeMounts:
            - name: worker-config
              mountPath: /etc/datadog/synthetics-check-runner.json
              subPath: synthetics-check-runner.json
    syncPolicy:
      automated:
        prune: true
      syncOptions:
        - CreateNamespace=true
        - Validate=false
      retry:
        limit: 5
        backoff:
          duration: 5s
          factor: 2
          maxDuration: 3m
    ignoreDifferences:
      - group: apps
        kind: Deployment
        jsonPointers:
          - /spec/replicas
