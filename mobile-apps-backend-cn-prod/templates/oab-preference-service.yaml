apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: oab-preference-service
  namespace: argocd
spec:
  project: digital-services
  source:
    repoURL: ****************************:D9/one-app/customer/oab-preference-service-config.git
    targetRevision: dev
    path: mab-cn-prod
  destination:
    server: https://kubernetes.default.svc
    namespace: oab
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - Validate=false
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  ignoreDifferences:
    - group: apps
      kind: Deployment
      jsonPointers:
        - /spec/replicas